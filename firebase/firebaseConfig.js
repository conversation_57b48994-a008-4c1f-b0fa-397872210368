import { initializeApp } from 'firebase/app';
import { getFirestore, collection } from 'firebase/firestore';
import { initializeAuth, getReactNativePersistence } from 'firebase/auth';
import ReactNativeAsyncStorage from '@react-native-async-storage/async-storage';

// Initialize Firebase
const firebaseConfig = {
  apiKey: 'AIzaSyAfEs07YQsot_zoVPxsBHD63d1oHHeqcFo',
  authDomain: 'chefpal-a9abe.firebaseapp.com',
  projectId: 'chefpal-a9abe',
  storageBucket: 'chefpal-a9abe.firebasestorage.app',
  messagingSenderId: '116153101016',
  appId: '1:116153101016:ios:b9ea3929b35c9efad945ad',
  // measurementId: 'G-measurement-id',
};

const app = initializeApp(firebaseConfig);
// Initialize Firebase Authentication and get a reference to the service
const auth = initializeAuth(app, {
  persistence: getReactNativePersistence(ReactNativeAsyncStorage)
});

export const db = getFirestore(app);
export const dietPreferenceCollection = collection(db, 'dietPreferences');
export const conversationCollection = collection(db, 'conversations');
export const inventoryCollection = collection(db, 'inventory');
export const groceryListCollection = collection(db, 'groceryList');
export const recipesCollection = collection(db, 'recipes');
export const usersCollection = collection(db, 'users');

export { app, auth };